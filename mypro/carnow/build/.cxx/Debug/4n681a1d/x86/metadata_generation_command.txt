                        -H/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.2.12479018
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.2.12479018
-DC<PERSON>KE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.2.12479018/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/mypro/carnow/build/app/intermediates/cxx/Debug/4n681a1d/obj/x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/mypro/carnow/build/app/intermediates/cxx/Debug/4n681a1d/obj/x86
-DCMAKE_BUILD_TYPE=Debug
-B/Users/<USER>/mypro/carnow/build/.cxx/Debug/4n681a1d/x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2