[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: x86", "file_": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/src/CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON '/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/android/.cxx/Debug/1q226b4x/x86/android_gradle_build.json' was up-to-date", "file_": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/src/CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/src/CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]