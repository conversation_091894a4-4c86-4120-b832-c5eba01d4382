import 'dart:developer' as developer;
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'google_auth_interface_v7.dart' hide GoogleSignInException, GoogleSignInExceptionCode;
import 'google_oauth_config.dart';

part 'google_auth_service.g.dart';

/// CarNow Google Authentication Service v7.1.1 (Production Ready)
/// خدمة مصادقة Google لتطبيق CarNow v7.1.1 (جاهزة للإنتاج)
///
/// This is the unified Google Sign-In service for CarNow, implementing the latest
/// v7.1.1 API with full async/await support and Forever Plan Architecture compliance.
///
/// ✅ Key Features:
/// - Full v7.1.1 API support with async/await
/// - Enhanced error handling with Arabic localization
/// - Production-ready reliability and performance
/// - Forever Plan Architecture compliance
/// - Riverpod integration with auto-disposal
/// - Comprehensive logging and monitoring
/// - Singleton pattern for optimal resource usage
/// - Real data only (zero mock data tolerance)
///
/// 🏗️ Forever Plan Compliance:
/// - Flutter UI Only → No business logic in UI
/// - Go API Integration → Ready for backend calls
/// - Supabase Integration → Via backend API calls
/// - Real Data Only → No mock data in production
class GoogleAuthService implements IGoogleAuthServiceV7 {
  // Singleton instance
  static GoogleAuthService? _instance;
  static GoogleAuthService get instance => _instance ??= GoogleAuthService._();
  
  GoogleAuthService._();

  // Configuration
  bool _isInitialized = false;
  List<String> _configuredScopes = ['email', 'profile'];
  GoogleSignIn? _googleSignIn;
  
  // Manual state management (v7 doesn't have automatic state tracking)
  GoogleUserInfo? _currentUser;

  @override
  bool get isConfigured => _isInitialized && _googleSignIn != null;

  @override
  bool get supportsAuthenticate {
    if (!isConfigured) return false;
    
    try {
      return _googleSignIn!.supportsAuthenticate();
    } catch (e) {
      developer.log(
        'Error checking authenticate support: $e',
        name: 'GoogleAuthService',
        level: 900,
      );
      return false;
    }
  }

  @override
  GoogleUserInfo? get currentUser => _currentUser;

  @override
  Future<void> initialize({
    required String clientId,
    List<String> scopes = const ['email', 'profile'],
  }) async {
    try {
      developer.log(
        'Initializing Google Sign-In v7.1.1...',
        name: 'GoogleAuthService',
      );

      _configuredScopes = List.from(scopes);

      // Create GoogleSignIn singleton instance
      _googleSignIn = GoogleSignIn.instance;
      
      // Initialize the service (required in v7.1.1)
      await _googleSignIn!.initialize();

      _isInitialized = true;

      developer.log(
        '✅ Google Sign-In v7.1.1 initialized successfully',
        name: 'GoogleAuthService',
      );
    } catch (error, stackTrace) {
      developer.log(
        '❌ Failed to initialize Google Sign-In v7.1.1',
        error: error,
        stackTrace: stackTrace,
        name: 'GoogleAuthService',
        level: 1000,
      );
      
      _isInitialized = false;
      rethrow;
    }
  }

  @override
  Future<GoogleAuthResult> authenticate({
    List<String>? scopeHint,
  }) async {
    try {
      if (!isConfigured) {
        return const GoogleAuthResult.failure(
          error: 'Google Sign-In service not initialized',
          errorCode: 'service_not_initialized',
          details: 'Call initialize() before using authenticate()',
        );
      }

      if (!supportsAuthenticate) {
        return const GoogleAuthResult.failure(
          error: 'Platform does not support authenticate method',
          errorCode: 'platform_not_supported',
          details: 'Use web-specific sign-in flow for web platform',
        );
      }

      developer.log(
        '🔐 Starting Google authentication v7.1.1...',
        name: 'GoogleAuthService',
      );

      // Use the new authenticate() method from v7.1.1
      final googleUser = await _googleSignIn!.authenticate(
        scopeHint: scopeHint ?? _configuredScopes,
      );

      // Get authentication tokens (now synchronous in v7.1.1)
      final auth = googleUser.authentication;
      
      if (auth.idToken == null) {
        return const GoogleAuthResult.failure(
          error: 'Failed to get ID token from Google',
          errorCode: 'missing_id_token',
          details: 'ID token is required for authentication',
        );
      }

      // Create user info
      final userInfo = GoogleUserInfo(
        id: googleUser.id,
        email: googleUser.email,
        displayName: googleUser.displayName ?? '',
        firstName: googleUser.displayName?.split(' ').first,
        lastName: googleUser.displayName?.split(' ').skip(1).join(' '),
        photoUrl: googleUser.photoUrl,
      );

      // Update current user state
      _currentUser = userInfo;

      developer.log(
        '✅ Google authentication successful for: ${googleUser.email}',
        name: 'GoogleAuthService',
      );

      return GoogleAuthResult.success(
        idToken: auth.idToken!,
        accessToken: '', // v7.1.1 doesn't provide access token in authentication
        userInfo: userInfo,
        expiryDate: null, // v7.1.1 doesn't provide expiry date directly
      );

    } on GoogleSignInException catch (e) {
      developer.log(
        '❌ Google Sign-In exception: ${e.code}',
        error: e,
        name: 'GoogleAuthService',
        level: 900,
      );

      // Handle specific Google Sign-In exceptions
      if (e.code == GoogleSignInExceptionCode.canceled) {
        return const GoogleAuthResult.cancelled(
          reason: 'User cancelled Google sign-in',
        );
      } else {
        return GoogleAuthResult.failure(
          error: _getArabicErrorMessage(e.code),
          errorCode: e.code.toString(),
          details: e.details?.toString(),
        );
      }
    } catch (error, stackTrace) {
      developer.log(
        '❌ Unexpected error during Google authentication',
        error: error,
        stackTrace: stackTrace,
        name: 'GoogleAuthService',
        level: 1000,
      );

      return GoogleAuthResult.failure(
        error: 'حدث خطأ غير متوقع أثناء تسجيل الدخول بـ Google',
        errorCode: 'unexpected_error',
        details: error.toString(),
      );
    }
  }

  @override
  Future<GoogleAuthResult?> attemptLightweightAuthentication() async {
    try {
      if (!isConfigured) {
        developer.log(
          'Google Sign-In service not initialized for lightweight auth',
          name: 'GoogleAuthService',
          level: 900,
        );
        return null;
      }

      developer.log(
        '🔄 Attempting lightweight authentication v7.1.1...',
        name: 'GoogleAuthService',
      );

      // Use the new attemptLightweightAuthentication() method from v7.1.1
      final result = _googleSignIn!.attemptLightweightAuthentication();
      
      GoogleSignInAccount? googleUser;
      
      // Handle both sync and async returns
      if (result is Future<GoogleSignInAccount?>) {
        googleUser = await result;
      } else {
        googleUser = result as GoogleSignInAccount?;
      }

      if (googleUser == null) {
        developer.log(
          'No user available for lightweight authentication',
          name: 'GoogleAuthService',
        );
        _currentUser = null;
        return null;
      }

      // Get authentication tokens (synchronous in v7.1.1)
      final auth = googleUser.authentication;
      
      if (auth.idToken == null) {
        developer.log(
          'No ID token available for lightweight authentication',
          name: 'GoogleAuthService',
          level: 900,
        );
        return null;
      }

      // Create user info
      final userInfo = GoogleUserInfo(
        id: googleUser.id,
        email: googleUser.email,
        displayName: googleUser.displayName ?? '',
        firstName: googleUser.displayName?.split(' ').first,
        lastName: googleUser.displayName?.split(' ').skip(1).join(' '),
        photoUrl: googleUser.photoUrl,
      );

      // Update current user state
      _currentUser = userInfo;

      developer.log(
        '✅ Lightweight authentication successful for: ${googleUser.email}',
        name: 'GoogleAuthService',
      );

      return GoogleAuthResult.success(
        idToken: auth.idToken!,
        accessToken: '', // v7.1.1 doesn't provide access token
        userInfo: userInfo,
        expiryDate: null,
      );

    } catch (error, stackTrace) {
      developer.log(
        '❌ Error during lightweight authentication',
        error: error,
        stackTrace: stackTrace,
        name: 'GoogleAuthService',
        level: 900,
      );
      
      _currentUser = null;
      return null;
    }
  }

  @override
  Future<void> signOut() async {
    try {
      if (!isConfigured) {
        developer.log(
          'Google Sign-In service not initialized for sign out',
          name: 'GoogleAuthService',
          level: 900,
        );
        return;
      }

      developer.log(
        '🚪 Signing out from Google v7.1.1...',
        name: 'GoogleAuthService',
      );

      await _googleSignIn!.signOut();
      _currentUser = null;

      developer.log(
        '✅ Google sign out successful',
        name: 'GoogleAuthService',
      );
    } catch (error, stackTrace) {
      developer.log(
        '❌ Error during Google sign out',
        error: error,
        stackTrace: stackTrace,
        name: 'GoogleAuthService',
        level: 900,
      );
      
      // Clear local state even if sign out fails
      _currentUser = null;
      rethrow;
    }
  }

  @override
  Future<void> disconnect() async {
    try {
      if (!isConfigured) {
        developer.log(
          'Google Sign-In service not initialized for disconnect',
          name: 'GoogleAuthService',
          level: 900,
        );
        return;
      }

      developer.log(
        '🔌 Disconnecting from Google v7.1.1...',
        name: 'GoogleAuthService',
      );

      await _googleSignIn!.disconnect();
      _currentUser = null;

      developer.log(
        '✅ Google disconnect successful',
        name: 'GoogleAuthService',
      );
    } catch (error, stackTrace) {
      developer.log(
        '❌ Error during Google disconnect',
        error: error,
        stackTrace: stackTrace,
        name: 'GoogleAuthService',
        level: 900,
      );
      
      // Clear local state even if disconnect fails
      _currentUser = null;
      rethrow;
    }
  }

  /// Get Arabic error message for Google Sign-In exception
  /// الحصول على رسالة خطأ بالعربية لاستثناء Google Sign-In
  String _getArabicErrorMessage(GoogleSignInExceptionCode code) {
    switch (code) {
      case GoogleSignInExceptionCode.canceled:
        return 'تم إلغاء تسجيل الدخول. يرجى المحاولة مرة أخرى إذا كنت تريد المتابعة.';
      case GoogleSignInExceptionCode.interrupted:
        return 'تم مقاطعة تسجيل الدخول. يرجى المحاولة مرة أخرى.';
      case GoogleSignInExceptionCode.clientConfigurationError:
        return 'يوجد مشكلة في تكوين تسجيل الدخول بـ Google. يرجى التواصل مع الدعم.';
      case GoogleSignInExceptionCode.providerConfigurationError:
        return 'تسجيل الدخول بـ Google غير متاح حالياً. يرجى المحاولة لاحقاً أو التواصل مع الدعم.';
      case GoogleSignInExceptionCode.uiUnavailable:
        return 'تسجيل الدخول بـ Google غير متاح حالياً. يرجى المحاولة لاحقاً أو التواصل مع الدعم.';
      case GoogleSignInExceptionCode.userMismatch:
        return 'يوجد مشكلة مع حسابك. يرجى تسجيل الخروج والمحاولة مرة أخرى.';
      case GoogleSignInExceptionCode.unknownError:
        return 'حدث خطأ غير متوقع أثناء تسجيل الدخول بـ Google. يرجى المحاولة مرة أخرى.';
    }
  }

  /// Reset the service (for testing purposes)
  /// إعادة تعيين الخدمة (لأغراض الاختبار)
  @visibleForTesting
  void reset() {
    _isInitialized = false;
    _googleSignIn = null;
    _currentUser = null;
    _configuredScopes = ['email', 'profile'];
  }
}

// =============================================================================
// RIVERPOD PROVIDERS
// =============================================================================

/// Provider for GoogleAuthService
/// موفر خدمة مصادقة Google
@riverpod
GoogleAuthService googleAuthService(Ref ref) {
  final service = GoogleAuthService.instance;

  // Auto-initialize the service with the proper client ID
  service.initialize(
    clientId: GoogleOAuthConfig.getClientId(),
    scopes: ['email', 'profile'],
  );

  return service;
}

/// Provider for current Google user
/// موفر المستخدم الحالي لـ Google
@riverpod
GoogleUserInfo? currentGoogleUser(Ref ref) {
  final service = ref.watch(googleAuthServiceProvider);
  return service.currentUser;
}

/// Provider for Google authentication state
/// موفر حالة مصادقة Google
@riverpod
bool isGoogleAuthenticated(Ref ref) {
  final user = ref.watch(currentGoogleUserProvider);
  return user != null;
}


